.faqWrapper{padding:var(--space-16)0;position:relative;overflow:hidden;background-color:var(--section-bg-faq);color:var(--section-text-faq);--text-primary:var(--section-text-faq);--text-secondary:rgba(255, 255, 255, 0.85);--text-tertiary:rgba(255, 255, 255, 0.65);--border-light:rgba(255, 255, 255, 0.12);--border-medium:rgba(255, 255, 255, 0.2)}.backgroundGradient{display:none}.container{position:relative;z-index:1;max-width:var(--container-xl);margin:0 auto;padding:0 var(--space-4)}.contentGrid{display:grid;grid-template-columns:1fr 2fr;gap:var(--space-8);align-items:center;margin-top:var(--space-10)}.visualElement{position:relative;height:100%;min-height:500px;border-radius:var(--radius-xl);overflow:hidden;background-color:rgba(255,255,255,.03);border:1px solid var(--border-light);box-shadow:var(--shadow-lg);display:flex;align-items:center;justify-content:center}.visualElementInner{position:absolute;inset:0;background:url(/images/gradients/g10.webp);background-size:cover;background-repeat:no-repeat;opacity:.5;z-index:0}.visualElementGrid{position:absolute;inset:0;background-image:linear-gradient(to right,rgba(255,255,255,.02) 1px,transparent 1px),linear-gradient(to bottom,rgba(255,255,255,.02) 1px,transparent 1px);background-size:20px 20px;opacity:.5;z-index:1}.visualElementContent{position:relative;z-index:2;padding:var(--space-8);text-align:center;color:var(--text-primary)}.textOrg{text-align:center;margin-bottom:var(--space-10);position:relative;z-index:1}.gradientText{font-size:var(--text-4xl);font-weight:var(--font-bold);margin-bottom:var(--space-4);background:linear-gradient(to right,var(--accent-orange),var(--accent-yellow));background-clip:text;color:transparent}.subtitle{font-size:var(--text-xl);color:var(--text-tertiary);margin-top:var(--space-4);max-width:600px;margin-left:auto;margin-right:auto}.faqList{max-width:100%;margin:0 auto;padding:0 var(--space-4);position:relative;z-index:1}.faqItem{background:rgba(255,255,255,.03);border-radius:var(--radius-lg);margin-bottom:var(--space-4);overflow:hidden;cursor:pointer;border:1px solid var(--border-light);transition:transform var(--transition-normal),box-shadow var(--transition-normal),background var(--transition-normal),border-color var(--transition-normal)}.faqItem:hover{background:rgba(255,255,255,.05);transform:translateY(-2px);border-color:var(--border-medium);box-shadow:var(--shadow-sm)}.faqQuestion{display:flex;justify-content:space-between;align-items:center;padding:var(--space-6)}.questionText{margin:0;font-size:var(--text-lg);color:var(--text-primary);font-weight:var(--font-medium)}.faqIcon{font-size:var(--text-xl);color:var(--accent-orange);transition:transform var(--transition-normal);width:24px;height:24px;display:flex;align-items:center;justify-content:center;background:rgba(255,153,0,.1);border-radius:50%}.open .faqIcon{transform:rotate(180deg);background:rgba(255,153,0,.2)}.faqAnswer{max-height:0;opacity:0;padding:0 var(--space-6);overflow:hidden}.open .faqAnswer{max-height:500px;opacity:1;padding:0 var(--space-6) var(--space-6)}.answerText{margin:0;color:var(--text-secondary);line-height:var(--leading-relaxed);font-size:var(--text-base)}@media (max-width:768px){.contentGrid{grid-template-columns:1fr;gap:var(--space-8)}.faqWrapper{padding:var(--space-12)0}.gradientText{font-size:var(--text-3xl)}.subtitle{font-size:var(--text-lg)}.questionText{font-size:var(--text-base)}.faqQuestion{padding:var(--space-4)}.open .faqAnswer{padding:0 var(--space-4) var(--space-4)}.visualElement{min-height:400px;display:none}}@media (max-width:480px){.faqWrapper{padding:var(--space-8)0}.gradientText{font-size:var(--text-2xl)}.subtitle{font-size:var(--text-base)}.faqList{padding:0 var(--space-2)}}