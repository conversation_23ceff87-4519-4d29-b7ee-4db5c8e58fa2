.pricingSection{padding:var(--section-spacing-y)0;background-color:var(--bg);position:relative}.container{width:100%}.sectionHeading{text-align:center;margin-bottom:3rem}.sectionTitle{font-family:var(--font-bwgradual);font-size:var(--text-section-heading);font-weight:600;color:var(--fg);margin:1rem 0;letter-spacing:-.01em}.sectionDescription{font-size:1.125rem;color:var(--text-secondary);max-width:600px;margin:0 auto;line-height:var(--leading-relaxed)}.currencySelector{display:flex;justify-content:center;margin-bottom:3rem}.currencyDropdown{position:relative}.dropdownTrigger{display:flex;align-items:center;gap:.5rem;padding:.75rem 1rem;background:var(--surface-dim);border:1px solid var(--border-soft);border-radius:.75rem;color:var(--fg);font-size:.875rem;font-weight:500;cursor:pointer;transition:all .3s ease;backdrop-filter:blur(4px);-webkit-backdrop-filter:blur(4px)}.dropdownTrigger:hover{background:rgba(255,255,255,.05);border-color:var(--border-strong)}.currencyMenu{position:absolute;top:calc(100% + .5rem);left:0;right:0;background:var(--surface-dim);border:1px solid var(--border-soft);border-radius:.75rem;backdrop-filter:blur(8px);-webkit-backdrop-filter:blur(8px);box-shadow:0 10px 40px -20px rgba(0,0,0,.5);z-index:10;overflow:hidden}.currencyOption{display:block;width:100%;padding:.75rem 1rem;background:0 0;border:0;color:var(--text-secondary);font-size:.875rem;text-align:left;cursor:pointer;transition:all .2s ease}.currencyOption:hover{background:rgba(255,255,255,.05);color:var(--fg)}.currencyOption.active{background:rgba(61,158,238,.12);color:var(--secondary)}.pricingCard{max-width:800px;margin:0 auto 4rem auto;background:var(--surface-dim);border:1px solid var(--border-soft);border-radius:2rem;overflow:hidden;backdrop-filter:blur(4px);-webkit-backdrop-filter:blur(4px)}.pricingHeader{padding:3rem 3rem 2rem 3rem;text-align:center;background:linear-gradient(135deg,rgba(61,158,238,.05)0,rgba(0,236,240,.05) 100%);border-bottom:1px solid var(--border-soft)}.priceDisplay{display:flex;align-items:baseline;justify-content:center;gap:.5rem;margin-bottom:2rem}.priceAmount{font-family:var(--font-bwgradual);font-size:clamp(3rem,8vw,5rem);font-weight:700;color:var(--fg);letter-spacing:-.02em}.pricePeriod{font-size:1.5rem;color:var(--text-secondary);font-weight:500}.billingToggle{display:flex;flex-direction:column;align-items:center;gap:1rem}.toggleWrapper{display:flex;background:var(--surface-dim);border:1px solid var(--border-soft);border-radius:1rem;padding:.25rem;position:relative}.toggleOption{position:relative;padding:.75rem 1.5rem;background:0 0;border:0;color:var(--text-secondary);font-size:.875rem;font-weight:500;cursor:pointer;transition:all .3s ease;border-radius:.75rem;display:flex;align-items:center;gap:.5rem}.toggleOption.active{background:var(--primary);color:#fff;box-shadow:0 4px 12px rgba(0,236,240,.3)}.savingsBadge{padding:.25rem .5rem;background:rgba(255,255,255,.2);border-radius:.375rem;font-size:.7rem;font-weight:600}.savingsNote{font-size:.875rem;color:var(--primary);margin:0;font-weight:500}.pricingContent{padding:3rem}.featuresSection{margin-bottom:2rem}.featuresTitle{font-family:var(--font-bwgradual);font-size:1.25rem;font-weight:600;color:var(--fg);margin:0 0 1.5rem 0}.featuresList{list-style:none;padding:0;margin:0;display:flex;flex-direction:column;gap:1rem}.featureItem{display:flex;align-items:center;gap:.75rem;color:var(--text-secondary);font-size:.875rem;line-height:var(--leading-relaxed)}.featureItem i{color:var(--primary);font-size:1rem;flex-shrink:0}.pricingCTA{text-align:center}.ctaNote{margin:1rem 0 0 0;font-size:.875rem;color:var(--text-tertiary)}.examplesSection{margin-bottom:4rem}.examplesTitle{font-family:var(--font-bwgradual);font-size:2rem;font-weight:600;color:var(--fg);text-align:center;margin:0 0 1rem 0;letter-spacing:-.01em}.examplesDescription{font-size:1rem;color:var(--text-secondary);text-align:center;margin:0 0 3rem 0;max-width:600px;margin-left:auto;margin-right:auto}.examplesGrid{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:2rem}.exampleCard{padding:2rem;background:var(--surface-dim);border:1px solid var(--border-soft);border-radius:1.5rem;backdrop-filter:blur(4px);-webkit-backdrop-filter:blur(4px);transition:all .3s ease}.exampleCard:hover{background:rgba(255,255,255,.05);border-color:var(--border-strong);transform:translateY(-4px)}.exampleTitle{font-family:var(--font-bwgradual);font-size:1.125rem;font-weight:600;color:var(--fg);margin:0 0 .5rem 0}.exampleDescription{color:var(--text-secondary);font-size:.875rem;margin:0 0 1.5rem 0;line-height:var(--leading-relaxed)}.exampleMeta{display:flex;flex-direction:column;gap:.75rem}.exampleTimeframe{display:flex;align-items:center;gap:.5rem;color:var(--text-tertiary);font-size:.8rem}.exampleTimeframe i{font-size:.75rem}.exampleCost{display:flex;justify-content:space-between;align-items:center}.costLabel{font-size:.8rem;color:var(--text-tertiary)}.costValue{font-weight:600;color:var(--secondary);font-size:.875rem}.bottomCTA{display:flex;align-items:center;justify-content:space-between;padding:2rem;background:var(--surface-dim);border:1px solid var(--border-soft);border-radius:1.5rem;backdrop-filter:blur(4px);-webkit-backdrop-filter:blur(4px)}.ctaContent h3{font-family:var(--font-bwgradual);font-size:1.25rem;font-weight:600;color:var(--fg);margin:0 0 .5rem 0}.ctaContent p{color:var(--text-secondary);margin:0;font-size:.875rem}@media (max-width:1024px){.pricingHeader{padding:2.5rem 2rem 1.5rem 2rem}.pricingContent{padding:2rem}.priceAmount{font-size:clamp(2.5rem,6vw,4rem)}.examplesGrid{gap:1.5rem}.exampleCard{padding:1.5rem}}@media (max-width:768px){.pricingHeader{padding:2rem 1.5rem 1.5rem 1.5rem}.pricingContent{padding:1.5rem}.priceAmount{font-size:clamp(2rem,8vw,3rem)}.pricePeriod{font-size:1.125rem}.toggleWrapper{width:100%}.toggleOption{flex:1;justify-content:center}.examplesGrid{grid-template-columns:1fr;gap:1rem}.exampleCard{padding:1.25rem}.bottomCTA{flex-direction:column;gap:1.5rem;text-align:center}.bottomCTA .btn{width:100%;max-width:280px}}@media (max-width:480px){.sectionHeading{margin-bottom:2rem}.pricingCard{border-radius:1.5rem}.pricingHeader{padding:1.5rem 1rem 1rem 1rem}.pricingContent{padding:1rem}.priceDisplay{flex-direction:column;gap:0}.priceAmount{font-size:clamp(1.75rem,10vw,2.5rem)}.pricePeriod{font-size:1rem}.toggleOption{padding:.5rem 1rem;font-size:.8rem}.savingsBadge{font-size:.65rem}.featuresTitle{font-size:1.125rem}.featureItem{font-size:.8rem}.examplesTitle{font-size:1.5rem}.examplesDescription{font-size:.875rem}.exampleCard{padding:1rem}.exampleTitle{font-size:1rem}.exampleDescription{font-size:.8rem}.bottomCTA{padding:1.5rem}.ctaContent h3{font-size:1.125rem}.ctaContent p{font-size:.8rem}}@media (prefers-reduced-motion:reduce){.toggleOption,.exampleCard{transition:none}.exampleCard:hover{transform:none}}